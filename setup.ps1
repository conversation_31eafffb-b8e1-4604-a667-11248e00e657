# 海康威视摄像头监控系统 - 环境检查脚本
# 检查开发环境是否满足项目要求

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "海康威视摄像头监控系统 - 环境检查" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查PowerShell版本
Write-Host "检查PowerShell版本..." -ForegroundColor Yellow
$psVersion = $PSVersionTable.PSVersion
if ($psVersion.Major -ge 5) {
    Write-Host "✓ PowerShell版本: $($psVersion.ToString())" -ForegroundColor Green
} else {
    Write-Host "✗ PowerShell版本过低: $($psVersion.ToString())" -ForegroundColor Red
    Write-Host "  需要PowerShell 5.0或更高版本" -ForegroundColor Red
}
Write-Host ""

# 检查操作系统
Write-Host "检查操作系统..." -ForegroundColor Yellow
$osInfo = Get-WmiObject -Class Win32_OperatingSystem
Write-Host "✓ 操作系统: $($osInfo.Caption)" -ForegroundColor Green
Write-Host "✓ 架构: $($env:PROCESSOR_ARCHITECTURE)" -ForegroundColor Green
Write-Host ""

# 检查Visual Studio
Write-Host "检查Visual Studio安装..." -ForegroundColor Yellow
$vsInstallations = @()

# 检查Visual Studio 2019/2022
$vswhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
if (Test-Path $vswhere) {
    $vsInstallations = & $vswhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -format json | ConvertFrom-Json
}

if ($vsInstallations.Count -gt 0) {
    foreach ($vs in $vsInstallations) {
        Write-Host "✓ Visual Studio: $($vs.displayName) ($($vs.installationVersion))" -ForegroundColor Green
        Write-Host "  路径: $($vs.installationPath)" -ForegroundColor Gray
    }
} else {
    Write-Host "✗ 未找到Visual Studio安装" -ForegroundColor Red
    Write-Host "  需要Visual Studio 2019或2022，包含C++工具" -ForegroundColor Red
}
Write-Host ""

# 检查CMake
Write-Host "检查CMake..." -ForegroundColor Yellow
try {
    $cmakeVersion = & cmake --version 2>$null
    if ($cmakeVersion) {
        $version = ($cmakeVersion[0] -split ' ')[2]
        Write-Host "✓ CMake版本: $version" -ForegroundColor Green
    } else {
        throw "CMake not found"
    }
} catch {
    Write-Host "✗ 未找到CMake" -ForegroundColor Red
    Write-Host "  请从 https://cmake.org/download/ 下载安装" -ForegroundColor Red
}
Write-Host ""

# 检查Qt6
Write-Host "检查Qt6安装..." -ForegroundColor Yellow
$qtFound = $false
$qtPaths = @(
    "C:\Qt\6.5.0\msvc2022_64\bin\qmake.exe",
    "C:\Qt\6.4.0\msvc2022_64\bin\qmake.exe",
    "C:\Qt\6.6.0\msvc2022_64\bin\qmake.exe",
    "C:\Qt\6.7.0\msvc2022_64\bin\qmake.exe"
)

foreach ($qtPath in $qtPaths) {
    if (Test-Path $qtPath) {
        $qtFound = $true
        $qtDir = Split-Path (Split-Path $qtPath)
        Write-Host "✓ 找到Qt6: $qtDir" -ForegroundColor Green
        
        # 检查Qt6环境变量
        if ($env:Qt6_DIR -eq $qtDir) {
            Write-Host "✓ Qt6_DIR环境变量已设置" -ForegroundColor Green
        } else {
            Write-Host "! Qt6_DIR环境变量未设置或不正确" -ForegroundColor Yellow
            Write-Host "  建议设置: Qt6_DIR=$qtDir" -ForegroundColor Yellow
        }
        break
    }
}

if (-not $qtFound) {
    Write-Host "✗ 未找到Qt6安装" -ForegroundColor Red
    Write-Host "  请从 https://www.qt.io/download 下载安装Qt6" -ForegroundColor Red
    Write-Host "  推荐版本: Qt 6.5.0 或更高版本" -ForegroundColor Red
}
Write-Host ""

# 检查海康威视SDK
Write-Host "检查海康威视SDK..." -ForegroundColor Yellow
$sdkPath = "hikvision_sdk\include"
if (Test-Path $sdkPath) {
    $hcnetsdkPath = Join-Path $sdkPath "HCNetSDK.h"
    $playctrlPath = Join-Path $sdkPath "PlayCtrl.h"
    
    if ((Test-Path $hcnetsdkPath) -and (Test-Path $playctrlPath)) {
        Write-Host "✓ SDK头文件存在" -ForegroundColor Green
        Write-Host "  注意: 当前使用的是模拟头文件" -ForegroundColor Yellow
        Write-Host "  实际部署时需要替换为真实的海康威视SDK" -ForegroundColor Yellow
    } else {
        Write-Host "✗ SDK头文件不完整" -ForegroundColor Red
    }
} else {
    Write-Host "✗ 未找到SDK目录" -ForegroundColor Red
}
Write-Host ""

# 检查项目文件
Write-Host "检查项目文件..." -ForegroundColor Yellow
$requiredFiles = @(
    "CMakeLists.txt",
    "src\main.cpp",
    "src\mainwindow.cpp",
    "src\cameramanager.cpp",
    "src\usermanager.cpp",
    "src\userlogindialog.cpp",
    "include\mainwindow.h",
    "include\cameramanager.h",
    "include\usermanager.h",
    "include\userlogindialog.h"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Host "✓ 所有项目文件完整" -ForegroundColor Green
} else {
    Write-Host "部分项目文件缺失" -ForegroundColor Red
}
Write-Host ""

# 总结
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "环境检查完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

if ($vsInstallations.Count -gt 0 -and $qtFound -and $allFilesExist) {
    Write-Host "✓ 环境配置良好，可以开始编译" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步:" -ForegroundColor Yellow
    Write-Host "1. 确保海康威视SDK文件完整" -ForegroundColor Yellow
    Write-Host "2. 运行 build.bat 编译项目" -ForegroundColor Yellow
    Write-Host "3. 将SDK的DLL文件复制到输出目录" -ForegroundColor Yellow
} else {
    Write-Host "✗ 环境配置不完整，请解决上述问题" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")