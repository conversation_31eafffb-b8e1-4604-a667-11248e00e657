# 海康威视摄像头监控系统

这是一个基于Qt6和海康威视SDK开发的摄像头监控应用程序，支持实时视频流显示和用户凭据管理。

## 功能特性

- 🎥 **实时视频监控**: 连接海康威视摄像头并显示实时视频流
- 🔐 **用户凭据管理**: 安全存储和管理多组用户名密码
- 🚀 **自动连接**: 程序启动时自动连接到最近使用的摄像头
- 🎨 **现代化界面**: 采用深色主题的现代化用户界面
- 📝 **连接日志**: 详细的连接状态和错误日志记录

## 系统要求

- **操作系统**: Windows 10/11 (64位)
- **编译器**: MSVC 2019 或更新版本
- **Qt版本**: Qt 6.2 或更新版本
- **海康威视SDK**: HCNetSDK 和 PlayCtrl 库

## 依赖库

### Qt6 组件
- Qt6::Core
- Qt6::Widgets
- Qt6::Multimedia
- Qt6::MultimediaWidgets

### 海康威视SDK
- HCNetSDK.lib
- PlayCtrl.lib
- 相关DLL文件

## 项目结构

```
C++/
├── CMakeLists.txt          # CMake构建配置
├── README.md               # 项目说明文档
├── include/                # 头文件目录
│   ├── mainwindow.h        # 主窗口类
│   ├── cameramanager.h     # 摄像头管理类
│   ├── usermanager.h       # 用户管理类
│   └── userlogindialog.h   # 用户管理对话框
├── src/                    # 源文件目录
│   ├── main.cpp            # 程序入口
│   ├── mainwindow.cpp      # 主窗口实现
│   ├── cameramanager.cpp   # 摄像头管理实现
│   ├── usermanager.cpp     # 用户管理实现
│   └── userlogindialog.cpp # 用户管理对话框实现
└── hikvision_sdk/          # 海康威视SDK目录（需要手动添加）
    ├── include/            # SDK头文件
    ├── lib/                # SDK库文件
    └── bin/                # SDK DLL文件
```

## 安装和编译

### 1. 准备开发环境

1. 安装 Visual Studio 2019 或更新版本
2. 安装 Qt6 (推荐使用Qt在线安装器)
3. 安装 CMake 3.16 或更新版本

### 2. 获取海康威视SDK

1. 从海康威视官网下载设备网络SDK
2. 解压SDK文件
3. 在项目根目录创建 `hikvision_sdk` 文件夹
4. 将SDK文件按以下结构放置：
   ```
   hikvision_sdk/
   ├── include/
   │   ├── HCNetSDK.h
   │   └── PlayCtrl.h
   ├── lib/
   │   ├── HCNetSDK.lib
   │   └── PlayCtrl.lib
   └── bin/
       ├── HCNetSDK.dll
       ├── PlayCtrl.dll
       └── 其他相关DLL文件
   ```

### 3. 编译项目

```bash
# 创建构建目录
mkdir build
cd build

# 配置CMake（替换为你的Qt安装路径）
cmake .. -DCMAKE_PREFIX_PATH="C:/Qt/6.5.0/msvc2019_64"

# 编译项目
cmake --build . --config Release
```

### 4. 运行程序

编译完成后，可执行文件位于 `build/Release/HikvisionViewer.exe`

## 使用说明

### 首次使用

1. **启动程序**: 运行 `HikvisionViewer.exe`
2. **输入IP地址**: 在IP地址文本框中输入摄像头的IP地址
3. **用户管理**: 点击"用户管理"按钮添加摄像头的用户名和密码
4. **连接摄像头**: 点击"连接摄像头"按钮开始连接

### 默认凭据

程序预设了以下默认用户凭据：
- admin / fssz2016
- admin / Fssz123456
- admin / admin
- admin / 123456
- admin / 12345
- root / root
- user / user

### 用户管理

1. 点击"用户管理"按钮打开用户管理对话框
2. 在右侧面板输入新的用户名和密码
3. 点击"添加用户"保存凭据
4. 左侧列表显示所有已保存的用户（密码不可见）
5. 选择用户后可以点击"删除选中用户"进行删除

### 自动连接

- 程序会记住最后成功连接的IP地址
- 下次启动时会自动尝试连接到该IP地址
- 使用已保存的凭据进行自动认证

## 配置文件

程序配置和用户凭据保存在：
```
%APPDATA%/Camera Monitoring System/camera_credentials.ini
```

用户密码经过加密存储，确保安全性。

## 故障排除

### 常见问题

1. **SDK初始化失败**
   - 检查海康威视SDK文件是否正确放置
   - 确保所有DLL文件都在程序目录中

2. **连接失败**
   - 检查摄像头IP地址是否正确
   - 确认网络连接正常
   - 验证用户名和密码是否正确
   - 检查摄像头是否支持网络连接

3. **视频显示问题**
   - 确认摄像头支持实时流传输
   - 检查网络带宽是否足够
   - 尝试重新连接摄像头

### 日志信息

程序在界面左下角显示详细的连接日志，包括：
- 连接尝试时间
- 连接状态变化
- 错误信息和错误代码

## 开发说明

### 架构设计

- **MainWindow**: 主界面管理，负责UI交互和状态显示
- **CameraManager**: 摄像头连接管理，封装海康威视SDK调用
- **UserManager**: 用户凭据管理，负责加密存储和检索
- **UserLoginDialog**: 用户管理界面，提供凭据的增删改查

### 扩展功能

可以考虑添加的功能：
- 多摄像头同时监控
- 录像功能
- 截图功能
- 云台控制
- 报警功能
- 网络发现摄像头

## 许可证

本项目仅供学习和研究使用。使用海康威视SDK需要遵守其相应的许可协议。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系

---

**注意**: 本项目需要合法的海康威视SDK授权才能正常使用。请确保遵守相关法律法规和SDK使用协议。