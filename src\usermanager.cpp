#include "usermanager.h"
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QCoreApplication>

const QString UserManager::ENCRYPTION_KEY = "HikvisionCameraViewer2024";

UserManager::UserManager(QObject *parent)
    : QObject(parent)
    , m_settings(nullptr)
{
    // Initialize settings
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configPath);
    
    QString settingsFile = configPath + "/camera_credentials.ini";
    m_settings = new QSettings(settingsFile, QSettings::IniFormat, this);
    
    loadCredentials();
}

UserManager::~UserManager()
{
    saveCredentials();
}

void UserManager::addCredentials(const QString &username, const QString &password)
{
    if (username.isEmpty() || password.isEmpty()) {
        return;
    }
    
    // Check if username already exists
    for (int i = 0; i < m_credentials.size(); ++i) {
        if (m_credentials[i].username == username) {
            // Update existing credentials
            m_credentials[i].password = password;
            saveCredentials();
            emit credentialsUpdated();
            return;
        }
    }
    
    // Add new credentials
    CameraCredentials newCreds;
    newCreds.username = username;
    newCreds.password = password;
    m_credentials.append(newCreds);
    
    saveCredentials();
    emit credentialsAdded(username);
    emit credentialsUpdated();
}

void UserManager::removeCredentials(const QString &username)
{
    for (int i = 0; i < m_credentials.size(); ++i) {
        if (m_credentials[i].username == username) {
            m_credentials.removeAt(i);
            saveCredentials();
            emit credentialsRemoved(username);
            emit credentialsUpdated();
            return;
        }
    }
}

QList<QString> UserManager::getUsernames() const
{
    QList<QString> usernames;
    for (const auto &cred : m_credentials) {
        usernames.append(cred.username);
    }
    return usernames;
}

bool UserManager::hasCredentials(const QString &username) const
{
    for (const auto &cred : m_credentials) {
        if (cred.username == username) {
            return true;
        }
    }
    return false;
}

CameraCredentials UserManager::getCredentials(const QString &username) const
{
    for (const auto &cred : m_credentials) {
        if (cred.username == username) {
            return cred;
        }
    }
    return CameraCredentials();
}

QList<CameraCredentials> UserManager::getAllCredentials() const
{
    return m_credentials;
}

CameraCredentials UserManager::findWorkingCredentials(const QString &ip, CameraManager *cameraManager)
{
    if (!cameraManager || ip.isEmpty()) {
        return CameraCredentials();
    }
    
    // Try each stored credential
    for (const auto &cred : m_credentials) {
        qDebug() << "Trying credentials:" << cred.username;
        
        // Create a temporary camera manager for testing
        // Note: In a real implementation, you might want to use a different approach
        // to avoid multiple connections
        
        // For now, we'll return the first available credentials
        // In a production environment, you should implement proper credential testing
        if (!cred.username.isEmpty() && !cred.password.isEmpty()) {
            return cred;
        }
    }
    
    return CameraCredentials();
}

void UserManager::initializeDefaultCredentials()
{
    // Add default credentials if none exist
    if (m_credentials.isEmpty()) {
        addCredentials("admin", "fssz2016");
        addCredentials("admin", "Fssz123456");
        
        // Add some common default credentials
        addCredentials("admin", "admin");
        addCredentials("admin", "123456");
        addCredentials("admin", "12345");
        addCredentials("root", "root");
        addCredentials("user", "user");
    }
}

QString UserManager::encryptPassword(const QString &password) const
{
    // Simple XOR encryption (not secure for production use)
    QByteArray data = password.toUtf8();
    QByteArray key = ENCRYPTION_KEY.toUtf8();
    
    for (int i = 0; i < data.size(); ++i) {
        data[i] = data[i] ^ key[i % key.size()];
    }
    
    return QString::fromLatin1(data.toBase64());
}

QString UserManager::decryptPassword(const QString &encryptedPassword) const
{
    // Simple XOR decryption
    QByteArray data = QByteArray::fromBase64(encryptedPassword.toLatin1());
    QByteArray key = ENCRYPTION_KEY.toUtf8();
    
    for (int i = 0; i < data.size(); ++i) {
        data[i] = data[i] ^ key[i % key.size()];
    }
    
    return QString::fromUtf8(data);
}

void UserManager::loadCredentials()
{
    m_credentials.clear();
    
    int size = m_settings->beginReadArray("credentials");
    for (int i = 0; i < size; ++i) {
        m_settings->setArrayIndex(i);
        
        CameraCredentials cred;
        cred.username = m_settings->value("username").toString();
        QString encryptedPassword = m_settings->value("password").toString();
        cred.password = decryptPassword(encryptedPassword);
        
        if (!cred.username.isEmpty() && !cred.password.isEmpty()) {
            m_credentials.append(cred);
        }
    }
    m_settings->endArray();
}

void UserManager::saveCredentials()
{
    m_settings->beginWriteArray("credentials");
    for (int i = 0; i < m_credentials.size(); ++i) {
        m_settings->setArrayIndex(i);
        m_settings->setValue("username", m_credentials[i].username);
        m_settings->setValue("password", encryptPassword(m_credentials[i].password));
    }
    m_settings->endArray();
    m_settings->sync();
}