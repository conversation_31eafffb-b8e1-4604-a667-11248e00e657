#ifndef USERMANAGER_H
#define USERMANAGER_H

#include <QObject>
#include <QString>
#include <QList>
#include <QSettings>
#include <QCryptographicHash>
#include <QByteArray>
#include "cameramanager.h"

class UserManager : public QObject
{
    Q_OBJECT

public:
    explicit UserManager(QObject *parent = nullptr);
    ~UserManager();
    
    // User management
    void addCredentials(const QString &username, const QString &password);
    void removeCredentials(const QString &username);
    QList<QString> getUsernames() const;
    bool hasCredentials(const QString &username) const;
    
    // Authentication
    CameraCredentials getCredentials(const QString &username) const;
    QList<CameraCredentials> getAllCredentials() const;
    
    // Try to authenticate with stored credentials
    CameraCredentials findWorkingCredentials(const QString &ip, class CameraManager *cameraManager);
    
    // Default credentials
    void initializeDefaultCredentials();
    
signals:
    void credentialsAdded(const QString &username);
    void credentialsRemoved(const QString &username);
    void credentialsUpdated();
    
private:
    QString encryptPassword(const QString &password) const;
    QString decryptPassword(const QString &encryptedPassword) const;
    void loadCredentials();
    void saveCredentials();
    
    QSettings *m_settings;
    QList<CameraCredentials> m_credentials;
    
    // Simple encryption key (in real application, use more secure method)
    static const QString ENCRYPTION_KEY;
};

#endif // USERMANAGER_H