#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QMessageBox>
#include <QTimer>
#include <QWidget>
#include <QTextEdit>
#include <QSplitter>
#include "cameramanager.h"
#include "usermanager.h"
#include "userlogindialog.h"

QT_BEGIN_NAMESPACE
class QLabel;
class QLineEdit;
class QPushButton;
class QVBoxLayout;
class QHBoxLayout;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onConnectCamera();
    void onDisconnectCamera();
    void onUserManagement();
    void onCameraConnected();
    void onCameraDisconnected();
    void onCameraError(const QString &error);
    void updateConnectionStatus();

private:
    void setupUI();
    void setupConnections();
    void updateButtonStates();
    
    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    
    // Control Panel
    QGroupBox *m_controlGroup;
    QLineEdit *m_ipLineEdit;
    QPushButton *m_connectButton;
    QPushButton *m_disconnectButton;
    QPushButton *m_userManagementButton;
    QLabel *m_statusLabel;
    QTextEdit *m_logTextEdit;
    
    // Video Display
    QGroupBox *m_videoGroup;
    QLabel *m_videoLabel;
    
    // Managers
    CameraManager *m_cameraManager;
    UserManager *m_userManager;
    
    // Status
    QTimer *m_statusTimer;
    bool m_isConnected;
};

#endif // MAINWINDOW_H