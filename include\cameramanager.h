#ifndef CAMERAMANAGER_H
#define CAMERAMANAGER_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <QMutex>
#include <QThread>
#include <QLabel>
#include <QPixmap>
#include <QImage>

// Hikvision SDK headers
#ifdef _WIN32
#include "HCNetSDK.h"
#include "PlayCtrl.h"
#pragma comment(lib, "HCNetSDK.lib")
#pragma comment(lib, "PlayCtrl.lib")
#endif

struct CameraCredentials {
    QString username;
    QString password;
};

class CameraManager : public QObject
{
    Q_OBJECT

public:
    explicit CameraManager(QObject *parent = nullptr);
    ~CameraManager();
    
    bool initializeSDK();
    void cleanupSDK();
    
    bool connectToCamera(const QString &ip, const CameraCredentials &credentials);
    void disconnectFromCamera();
    
    bool isConnected() const { return m_isConnected; }
    QString getCurrentIP() const { return m_currentIP; }
    QString getLastError() const { return m_lastError; }
    
    void setVideoDisplay(QLabel *videoLabel) { m_videoLabel = videoLabel; }
    
public slots:
    void startPreview();
    void stopPreview();
    
signals:
    void connected();
    void disconnected();
    void error(const QString &errorMessage);
    void frameReceived(const QImage &frame);
    
private:
    static void CALLBACK RealDataCallBack(LONG lRealHandle, DWORD dwDataType, BYTE *pBuffer, DWORD dwBufSize, void *pUser);
    static void CALLBACK DecCBFun(long nPort, char *pBuf, long nSize, FRAME_INFO *pFrameInfo, long nUser, long nReserved2);
    
    void processVideoFrame(BYTE *pBuffer, DWORD dwBufSize);
    void setError(const QString &error);
    
    // Hikvision SDK variables
    LONG m_userID;
    LONG m_realPlayHandle;
    LONG m_playHandle;
    
    // Connection state
    bool m_isConnected;
    bool m_isPlaying;
    QString m_currentIP;
    QString m_lastError;
    
    // Video display
    QLabel *m_videoLabel;
    QMutex m_frameMutex;
    
    // SDK initialization
    static bool s_sdkInitialized;
    static int s_instanceCount;
};

#endif // CAMERAMANAGER_H