#include "userlogindialog.h"
#include <QMessageBox>
#include <QHeaderView>
#include <QSplitter>
#include <QGroupBox>
#include <QFormLayout>

UserLoginDialog::UserLoginDialog(UserManager *userManager, QWidget *parent)
    : QDialog(parent)
    , m_mainSplitter(nullptr)
    , m_userListGroup(nullptr)
    , m_userListWidget(nullptr)
    , m_removeButton(nullptr)
    , m_addUserGroup(nullptr)
    , m_usernameLineEdit(nullptr)
    , m_passwordLineEdit(nullptr)
    , m_addButton(nullptr)
    , m_closeButton(nullptr)
    , m_statusLabel(nullptr)
    , m_userManager(userManager)
{
    setWindowTitle("用户管理");
    setModal(true);
    resize(600, 400);
    setMinimumSize(500, 350);
    
    setupUI();
    setupConnections();
    refreshUserList();
}

UserLoginDialog::~UserLoginDialog()
{
}

void UserLoginDialog::setupUI()
{
    // Main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // Main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    
    // User list panel
    m_userListGroup = new QGroupBox("已保存的用户", this);
    m_userListGroup->setMinimumWidth(200);
    
    QVBoxLayout *listLayout = new QVBoxLayout(m_userListGroup);
    
    m_userListWidget = new QListWidget(this);
    m_userListWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    listLayout->addWidget(m_userListWidget);
    
    m_removeButton = new QPushButton("删除选中用户", this);
    m_removeButton->setEnabled(false);
    m_removeButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }");
    listLayout->addWidget(m_removeButton);
    
    // Add user panel
    m_addUserGroup = new QGroupBox("添加新用户", this);
    m_addUserGroup->setMinimumWidth(250);
    
    QVBoxLayout *addLayout = new QVBoxLayout(m_addUserGroup);
    
    // Form layout for input fields
    QFormLayout *formLayout = new QFormLayout();
    
    m_usernameLineEdit = new QLineEdit(this);
    m_usernameLineEdit->setPlaceholderText("输入用户名");
    formLayout->addRow("用户名:", m_usernameLineEdit);
    
    m_passwordLineEdit = new QLineEdit(this);
    m_passwordLineEdit->setPlaceholderText("输入密码");
    m_passwordLineEdit->setEchoMode(QLineEdit::Password);
    formLayout->addRow("密码:", m_passwordLineEdit);
    
    addLayout->addLayout(formLayout);
    
    // Buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    
    m_addButton = new QPushButton("添加用户", this);
    m_addButton->setEnabled(false);
    m_addButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }");
    
    buttonLayout->addWidget(m_addButton);
    buttonLayout->addStretch();
    
    addLayout->addLayout(buttonLayout);
    addLayout->addStretch();
    
    // Add to splitter
    m_mainSplitter->addWidget(m_userListGroup);
    m_mainSplitter->addWidget(m_addUserGroup);
    m_mainSplitter->setStretchFactor(0, 1);
    m_mainSplitter->setStretchFactor(1, 1);
    
    mainLayout->addWidget(m_mainSplitter);
    
    // Status label
    m_statusLabel = new QLabel(this);
    m_statusLabel->setStyleSheet("QLabel { color: #666; font-style: italic; padding: 5px; }");
    m_statusLabel->setText("提示: 用户名和密码将被加密保存");
    mainLayout->addWidget(m_statusLabel);
    
    // Bottom buttons
    QHBoxLayout *bottomLayout = new QHBoxLayout();
    bottomLayout->addStretch();
    
    m_closeButton = new QPushButton("关闭", this);
    m_closeButton->setStyleSheet("QPushButton { background-color: #666; color: white; font-weight: bold; padding: 8px; }");
    bottomLayout->addWidget(m_closeButton);
    
    mainLayout->addLayout(bottomLayout);
}

void UserLoginDialog::setupConnections()
{
    connect(m_addButton, &QPushButton::clicked, this, &UserLoginDialog::onAddCredentials);
    connect(m_removeButton, &QPushButton::clicked, this, &UserLoginDialog::onRemoveCredentials);
    connect(m_closeButton, &QPushButton::clicked, this, &QDialog::accept);
    
    connect(m_userListWidget, &QListWidget::itemSelectionChanged, this, &UserLoginDialog::onUserSelectionChanged);
    
    connect(m_usernameLineEdit, &QLineEdit::textChanged, this, &UserLoginDialog::validateInput);
    connect(m_passwordLineEdit, &QLineEdit::textChanged, this, &UserLoginDialog::validateInput);
    connect(m_passwordLineEdit, &QLineEdit::returnPressed, this, &UserLoginDialog::onAddCredentials);
    
    if (m_userManager) {
        connect(m_userManager, &UserManager::credentialsUpdated, this, &UserLoginDialog::onCredentialsUpdated);
    }
}

void UserLoginDialog::onAddCredentials()
{
    if (!validateInputFields()) {
        return;
    }
    
    QString username = m_usernameLineEdit->text().trimmed();
    QString password = m_passwordLineEdit->text();
    
    if (m_userManager->hasCredentials(username)) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this,
            "用户已存在",
            QString("用户 '%1' 已存在，是否要更新密码？").arg(username),
            QMessageBox::Yes | QMessageBox::No
        );
        
        if (reply != QMessageBox::Yes) {
            return;
        }
    }
    
    m_userManager->addCredentials(username, password);
    
    // Clear input fields
    clearInputFields();
    
    // Update status
    m_statusLabel->setText(QString("用户 '%1' 已成功添加/更新").arg(username));
    m_statusLabel->setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; padding: 5px; }");
    
    // Reset status after 3 seconds
    QTimer::singleShot(3000, [this]() {
        m_statusLabel->setText("提示: 用户名和密码将被加密保存");
        m_statusLabel->setStyleSheet("QLabel { color: #666; font-style: italic; padding: 5px; }");
    });
}

void UserLoginDialog::onRemoveCredentials()
{
    QListWidgetItem *currentItem = m_userListWidget->currentItem();
    if (!currentItem) {
        return;
    }
    
    QString username = currentItem->text();
    
    QMessageBox::StandardButton reply = QMessageBox::question(
        this,
        "确认删除",
        QString("确定要删除用户 '%1' 吗？").arg(username),
        QMessageBox::Yes | QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        m_userManager->removeCredentials(username);
        
        // Update status
        m_statusLabel->setText(QString("用户 '%1' 已删除").arg(username));
        m_statusLabel->setStyleSheet("QLabel { color: #f44336; font-weight: bold; padding: 5px; }");
        
        // Reset status after 3 seconds
        QTimer::singleShot(3000, [this]() {
            m_statusLabel->setText("提示: 用户名和密码将被加密保存");
            m_statusLabel->setStyleSheet("QLabel { color: #666; font-style: italic; padding: 5px; }");
        });
    }
}

void UserLoginDialog::onUserSelectionChanged()
{
    bool hasSelection = m_userListWidget->currentItem() != nullptr;
    m_removeButton->setEnabled(hasSelection);
}

void UserLoginDialog::onCredentialsUpdated()
{
    refreshUserList();
}

void UserLoginDialog::validateInput()
{
    bool isValid = validateInputFields();
    m_addButton->setEnabled(isValid);
}

void UserLoginDialog::refreshUserList()
{
    m_userListWidget->clear();
    
    if (!m_userManager) {
        return;
    }
    
    QList<QString> usernames = m_userManager->getUsernames();
    for (const QString &username : usernames) {
        QListWidgetItem *item = new QListWidgetItem(username);
        item->setIcon(QIcon(":/icons/user.png")); // You can add an icon if available
        m_userListWidget->addItem(item);
    }
    
    // Update status
    if (usernames.isEmpty()) {
        m_statusLabel->setText("当前没有保存的用户凭据");
    } else {
        m_statusLabel->setText(QString("当前已保存 %1 个用户凭据").arg(usernames.size()));
    }
}

void UserLoginDialog::clearInputFields()
{
    m_usernameLineEdit->clear();
    m_passwordLineEdit->clear();
    m_usernameLineEdit->setFocus();
}

bool UserLoginDialog::validateInputFields()
{
    QString username = m_usernameLineEdit->text().trimmed();
    QString password = m_passwordLineEdit->text();
    
    return !username.isEmpty() && !password.isEmpty();
}