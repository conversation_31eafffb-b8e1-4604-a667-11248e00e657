cmake_minimum_required(VERSION 3.16)
project(HikvisionViewer VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia MultimediaWidgets)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/hikvision_sdk/include)

# Source files
set(SOURCES
    src/main.cpp
    src/mainwindow.cpp
    src/cameramanager.cpp
    src/usermanager.cpp
    src/userlogindialog.cpp
)

# Header files
set(HEADERS
    include/mainwindow.h
    include/cameramanager.h
    include/usermanager.h
    include/userlogindialog.h
)

# UI files
set(UI_FILES
    ui/mainwindow.ui
    ui/userlogindialog.ui
)

# Create executable
add_executable(HikvisionViewer ${SOURCES} ${HEADERS} ${UI_FILES})

# Link Qt libraries
target_link_libraries(HikvisionViewer
    Qt6::Core
    Qt6::Widgets
    Qt6::Multimedia
    Qt6::MultimediaWidgets
)

# Link Hikvision SDK (Windows)
if(WIN32)
    target_link_libraries(HikvisionViewer
        ${CMAKE_CURRENT_SOURCE_DIR}/hikvision_sdk/lib/HCNetSDK.lib
        ${CMAKE_CURRENT_SOURCE_DIR}/hikvision_sdk/lib/PlayCtrl.lib
    )
endif()

# Copy DLLs to output directory (Windows)
if(WIN32)
    add_custom_command(TARGET HikvisionViewer POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        ${CMAKE_CURRENT_SOURCE_DIR}/hikvision_sdk/bin
        $<TARGET_FILE_DIR:HikvisionViewer>
    )
endif()