#ifndef USERLOGINDIALOG_H
#define USERLOGINDIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QListWidget>
#include <QMessageBox>
#include <QSplitter>
#include "usermanager.h"

QT_BEGIN_NAMESPACE
class QLineEdit;
class QPushButton;
class QLabel;
class QListWidget;
class QGroupBox;
QT_END_NAMESPACE

class UserLoginDialog : public QDialog
{
    Q_OBJECT

public:
    explicit UserLoginDialog(UserManager *userManager, QWidget *parent = nullptr);
    ~UserLoginDialog();

private slots:
    void onAddCredentials();
    void onRemoveCredentials();
    void onUserSelectionChanged();
    void onCredentialsUpdated();
    void validateInput();

private:
    void setupUI();
    void setupConnections();
    void refreshUserList();
    void clearInputFields();
    bool validateInputFields();
    
    // UI Components
    QSplitter *m_mainSplitter;
    
    // User List Panel
    QGroupBox *m_userListGroup;
    QListWidget *m_userListWidget;
    QPushButton *m_removeButton;
    
    // Add User Panel
    QGroupBox *m_addUserGroup;
    QLineEdit *m_usernameLineEdit;
    QLineEdit *m_passwordLineEdit;
    QPushButton *m_addButton;
    QPushButton *m_closeButton;
    
    // Status
    QLabel *m_statusLabel;
    
    // Manager
    UserManager *m_userManager;
};

#endif // USERLOGINDIALOG_H