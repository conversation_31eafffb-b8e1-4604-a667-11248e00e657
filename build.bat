@echo off
chcp 65001 >nul
echo ========================================
echo 海康威视摄像头监控系统 - 编译脚本
echo ========================================
echo.

:: 检查是否存在Qt环境变量
if "%Qt6_DIR%"=="" (
    echo 警告: 未设置Qt6_DIR环境变量
    echo 请设置Qt6_DIR指向Qt6安装目录，例如:
    echo set Qt6_DIR=C:\Qt\6.5.0\msvc2022_64
    echo.
    echo 尝试使用默认路径...
    set Qt6_DIR=C:\Qt\6.5.0\msvc2022_64
)

echo 使用Qt路径: %Qt6_DIR%
echo.

:: 检查Qt路径是否存在
if not exist "%Qt6_DIR%" (
    echo 错误: Qt路径不存在: %Qt6_DIR%
    echo 请检查Qt安装路径或设置正确的Qt6_DIR环境变量
    pause
    exit /b 1
)

:: 创建构建目录
if not exist "build" (
    echo 创建构建目录...
    mkdir build
)

cd build

:: 清理之前的构建文件
echo 清理之前的构建文件...
if exist "CMakeCache.txt" del "CMakeCache.txt"
if exist "CMakeFiles" rmdir /s /q "CMakeFiles"

:: 配置CMake
echo 配置CMake...
cmake .. -DCMAKE_PREFIX_PATH="%Qt6_DIR%" -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo 错误: CMake配置失败
    pause
    exit /b 1
)

:: 编译项目
echo 编译项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译完成!
echo ========================================
echo.
echo 可执行文件位置: build\Release\HikvisionViewer.exe
echo.
echo 注意事项:
echo 1. 确保海康威视SDK的DLL文件在系统PATH中或与可执行文件同目录
echo 2. 确保Qt6的DLL文件可以被找到
echo 3. 首次运行可能需要配置摄像头IP和凭据
echo.
set /p choice="是否现在运行程序? (y/n): "
if /i "%choice%"=="y" (
    if exist "Release\HikvisionViewer.exe" (
        echo 启动程序...
        start "" "Release\HikvisionViewer.exe"
    ) else (
        echo 错误: 找不到可执行文件
    )
)

echo.
echo 按任意键退出...
pause >nul