#ifndef _PLAY_CTRL_H_
#define _PLAY_CTRL_H_

// 简化的海康威视播放控制SDK头文件模拟
// 注意：这只是为了编译通过的模拟文件
// 实际使用时需要替换为真正的海康威视SDK头文件

#ifdef _WIN32
#include <windows.h>

// 基本数据类型定义
typedef long LONG;
typedef unsigned long DWORD;
typedef unsigned char BYTE;
typedef char CHAR;
typedef void* LPVOID;

// 帧信息结构体
typedef struct tagFRAME_INFO
{
    LONG nWidth;            // 画面宽度
    LONG nHeight;           // 画面高度
    LONG nStamp;            // 时间戳
    LONG nType;             // 数据类型
    LONG nFrameRate;        // 帧率
    DWORD dwFrameNum;       // 帧序号
} FRAME_INFO;

// 帧类型定义
#define T_AUDIO16       101     // 16位音频
#define T_AUDIO8        100     // 8位音频
#define T_MPEG4         1       // MPEG4视频
#define T_H264          2       // H264视频
#define T_YV12          3       // YV12格式
#define T_RGB32         4       // RGB32格式

// 解码回调函数类型
typedef void (CALLBACK *fDecCBFun)(long nPort, char *pBuf, long nSize, FRAME_INFO *pFrameInfo, long nUser, long nReserved2);

// API函数声明
extern "C" {
    // 初始化DirectDraw
    BOOL __stdcall PlayM4_InitDDraw();
    
    // 释放DirectDraw
    BOOL __stdcall PlayM4_ReleaseDDraw();
    
    // 获取空闲端口
    BOOL __stdcall PlayM4_GetPort(LONG *nPort);
    
    // 释放端口
    BOOL __stdcall PlayM4_FreePort(LONG nPort);
    
    // 设置解码回调
    BOOL __stdcall PlayM4_SetDecCallBackEx(LONG nPort, fDecCBFun DecCBFun, DWORD nUser, DWORD nReserved);
    
    // 打开流
    BOOL __stdcall PlayM4_OpenStream(LONG nPort, BYTE *pFileHeadBuf, DWORD nSize, DWORD nBufPoolSize);
    
    // 关闭流
    BOOL __stdcall PlayM4_CloseStream(LONG nPort);
    
    // 输入数据
    BOOL __stdcall PlayM4_InputData(LONG nPort, BYTE *pBuf, DWORD nSize);
    
    // 开始播放
    BOOL __stdcall PlayM4_Play(LONG nPort, HWND hWnd);
    
    // 停止播放
    BOOL __stdcall PlayM4_Stop(LONG nPort);
    
    // 暂停播放
    BOOL __stdcall PlayM4_Pause(LONG nPort, DWORD nPause);
    
    // 设置播放窗口
    BOOL __stdcall PlayM4_SetDisplayBuf(LONG nPort, DWORD nNum);
    
    // 获取当前播放时间
    DWORD __stdcall PlayM4_GetCurrentFrameRate(LONG nPort);
    
    // 获取播放时间
    DWORD __stdcall PlayM4_GetPlayedTime(LONG nPort);
    
    // 获取播放帧数
    DWORD __stdcall PlayM4_GetPlayedFrames(LONG nPort);
    
    // 设置播放速度
    BOOL __stdcall PlayM4_SetPlaySpeed(LONG nPort, float fCoff);
    
    // 获取最后错误
    DWORD __stdcall PlayM4_GetLastError(LONG nPort);
    
    // 刷新播放
    BOOL __stdcall PlayM4_RefreshPlay(LONG nPort);
    
    // 设置颜色键
    BOOL __stdcall PlayM4_SetColorKey(LONG nPort, COLORREF colorkey);
    
    // 获取图片质量
    BOOL __stdcall PlayM4_GetPictureQuality(LONG nPort, BOOL *bHighQuality);
    
    // 设置图片质量
    BOOL __stdcall PlayM4_SetPictureQuality(LONG nPort, BOOL bHighQuality);
    
    // 播放声音
    BOOL __stdcall PlayM4_PlaySound(LONG nPort);
    
    // 停止声音
    BOOL __stdcall PlayM4_StopSound();
    
    // 设置音量
    BOOL __stdcall PlayM4_SetVolume(LONG nPort, WORD nVolume);
    
    // 获取音量
    BOOL __stdcall PlayM4_GetVolume(LONG nPort, WORD *pVolume);
    
    // 重置缓冲区
    BOOL __stdcall PlayM4_ResetBuffer(LONG nPort, DWORD nBufType);
    
    // 获取缓冲区使用率
    LONG __stdcall PlayM4_GetBufferValue(LONG nPort);
}

#endif // _WIN32

#endif // _PLAY_CTRL_H_