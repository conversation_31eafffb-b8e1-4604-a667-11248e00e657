#include "mainwindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QSplitter>
#include <QGroupBox>
#include <QGridLayout>
#include <QFormLayout>
#include <QHeaderView>
#include <QDateTime>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_controlGroup(nullptr)
    , m_ipLineEdit(nullptr)
    , m_connectButton(nullptr)
    , m_disconnectButton(nullptr)
    , m_userManagementButton(nullptr)
    , m_statusLabel(nullptr)
    , m_logTextEdit(nullptr)
    , m_videoGroup(nullptr)
    , m_videoLabel(nullptr)
    , m_cameraManager(nullptr)
    , m_userManager(nullptr)
    , m_statusTimer(nullptr)
    , m_isConnected(false)
{
    setWindowTitle("海康威视摄像头监控系统");
    setMinimumSize(1200, 800);
    resize(1400, 900);
    
    // Initialize managers
    m_cameraManager = new CameraManager(this);
    m_userManager = new UserManager(this);
    
    // Initialize UI
    setupUI();
    setupConnections();
    
    // Initialize camera SDK
    if (!m_cameraManager->initializeSDK()) {
        QMessageBox::critical(this, "错误", "初始化海康威视SDK失败！");
    }
    
    // Initialize default credentials
    m_userManager->initializeDefaultCredentials();
    
    // Setup status timer
    m_statusTimer = new QTimer(this);
    connect(m_statusTimer, &QTimer::timeout, this, &MainWindow::updateConnectionStatus);
    m_statusTimer->start(1000); // Update every second
    
    // Auto-connect to last used camera
    QSettings settings;
    QString lastIP = settings.value("lastConnectedIP").toString();
    if (!lastIP.isEmpty()) {
        m_ipLineEdit->setText(lastIP);
        // Try to auto-connect after a short delay
        QTimer::singleShot(1000, this, &MainWindow::onConnectCamera);
    }
    
    updateButtonStates();
}

MainWindow::~MainWindow()
{
    if (m_cameraManager) {
        m_cameraManager->disconnectFromCamera();
        m_cameraManager->cleanupSDK();
    }
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    
    // Control panel
    m_controlGroup = new QGroupBox("控制面板", this);
    m_controlGroup->setMaximumWidth(350);
    m_controlGroup->setMinimumWidth(300);
    
    QVBoxLayout *controlLayout = new QVBoxLayout(m_controlGroup);
    
    // IP input
    QFormLayout *ipLayout = new QFormLayout();
    m_ipLineEdit = new QLineEdit(this);
    m_ipLineEdit->setPlaceholderText("请输入摄像头IP地址");
    m_ipLineEdit->setText("*************"); // Default IP
    ipLayout->addRow("摄像头IP:", m_ipLineEdit);
    controlLayout->addLayout(ipLayout);
    
    // Buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    m_connectButton = new QPushButton("连接摄像头", this);
    m_disconnectButton = new QPushButton("断开连接", this);
    m_userManagementButton = new QPushButton("用户管理", this);
    
    m_connectButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }");
    m_disconnectButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 8px; }");
    m_userManagementButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }");
    
    buttonLayout->addWidget(m_connectButton);
    buttonLayout->addWidget(m_disconnectButton);
    controlLayout->addLayout(buttonLayout);
    controlLayout->addWidget(m_userManagementButton);
    
    // Status label
    m_statusLabel = new QLabel("状态: 未连接", this);
    m_statusLabel->setStyleSheet("QLabel { color: #f44336; font-weight: bold; padding: 5px; }");
    controlLayout->addWidget(m_statusLabel);
    
    // Log text edit
    QLabel *logLabel = new QLabel("连接日志:", this);
    m_logTextEdit = new QTextEdit(this);
    m_logTextEdit->setMaximumHeight(200);
    m_logTextEdit->setReadOnly(true);
    controlLayout->addWidget(logLabel);
    controlLayout->addWidget(m_logTextEdit);
    
    controlLayout->addStretch();
    
    // Video display
    m_videoGroup = new QGroupBox("视频监控", this);
    QVBoxLayout *videoLayout = new QVBoxLayout(m_videoGroup);
    
    m_videoLabel = new QLabel(this);
    m_videoLabel->setMinimumSize(640, 480);
    m_videoLabel->setStyleSheet("QLabel { background-color: black; border: 2px solid #555; }");
    m_videoLabel->setAlignment(Qt::AlignCenter);
    m_videoLabel->setText("等待视频流...");
    m_videoLabel->setScaledContents(true);
    
    videoLayout->addWidget(m_videoLabel);
    
    // Add to splitter
    m_mainSplitter->addWidget(m_controlGroup);
    m_mainSplitter->addWidget(m_videoGroup);
    m_mainSplitter->setStretchFactor(0, 0);
    m_mainSplitter->setStretchFactor(1, 1);
    
    // Main layout
    QHBoxLayout *mainLayout = new QHBoxLayout(m_centralWidget);
    mainLayout->addWidget(m_mainSplitter);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    
    // Set video display for camera manager
    m_cameraManager->setVideoDisplay(m_videoLabel);
}

void MainWindow::setupConnections()
{
    connect(m_connectButton, &QPushButton::clicked, this, &MainWindow::onConnectCamera);
    connect(m_disconnectButton, &QPushButton::clicked, this, &MainWindow::onDisconnectCamera);
    connect(m_userManagementButton, &QPushButton::clicked, this, &MainWindow::onUserManagement);
    
    connect(m_cameraManager, &CameraManager::connected, this, &MainWindow::onCameraConnected);
    connect(m_cameraManager, &CameraManager::disconnected, this, &MainWindow::onCameraDisconnected);
    connect(m_cameraManager, &CameraManager::error, this, &MainWindow::onCameraError);
    
    connect(m_ipLineEdit, &QLineEdit::returnPressed, this, &MainWindow::onConnectCamera);
}

void MainWindow::onConnectCamera()
{
    QString ip = m_ipLineEdit->text().trimmed();
    if (ip.isEmpty()) {
        QMessageBox::warning(this, "警告", "请输入摄像头IP地址！");
        return;
    }
    
    if (m_isConnected) {
        QMessageBox::information(this, "提示", "摄像头已连接，请先断开当前连接！");
        return;
    }
    
    m_connectButton->setEnabled(false);
    m_connectButton->setText("连接中...");
    
    // Log connection attempt
    QString logMsg = QString("[%1] 尝试连接摄像头: %2")
                    .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                    .arg(ip);
    m_logTextEdit->append(logMsg);
    
    // Try to find working credentials
    CameraCredentials credentials = m_userManager->findWorkingCredentials(ip, m_cameraManager);
    
    if (credentials.username.isEmpty()) {
        m_connectButton->setEnabled(true);
        m_connectButton->setText("连接摄像头");
        QMessageBox::warning(this, "连接失败", "无法使用已保存的凭据连接到摄像头！\n请检查IP地址或添加正确的用户名密码。");
        return;
    }
    
    // Attempt connection
    bool success = m_cameraManager->connectToCamera(ip, credentials);
    
    m_connectButton->setEnabled(true);
    m_connectButton->setText("连接摄像头");
    
    if (success) {
        // Save last connected IP
        QSettings settings;
        settings.setValue("lastConnectedIP", ip);
    }
}

void MainWindow::onDisconnectCamera()
{
    m_cameraManager->disconnectFromCamera();
}

void MainWindow::onUserManagement()
{
    UserLoginDialog dialog(m_userManager, this);
    dialog.exec();
}

void MainWindow::onCameraConnected()
{
    m_isConnected = true;
    updateButtonStates();
    
    QString logMsg = QString("[%1] 摄像头连接成功: %2")
                    .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                    .arg(m_cameraManager->getCurrentIP());
    m_logTextEdit->append(logMsg);
    
    // Start preview
    m_cameraManager->startPreview();
}

void MainWindow::onCameraDisconnected()
{
    m_isConnected = false;
    updateButtonStates();
    
    QString logMsg = QString("[%1] 摄像头已断开连接")
                    .arg(QDateTime::currentDateTime().toString("hh:mm:ss"));
    m_logTextEdit->append(logMsg);
    
    m_videoLabel->setText("等待视频流...");
}

void MainWindow::onCameraError(const QString &error)
{
    QString logMsg = QString("[%1] 错误: %2")
                    .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                    .arg(error);
    m_logTextEdit->append(logMsg);
    
    QMessageBox::critical(this, "摄像头错误", error);
}

void MainWindow::updateConnectionStatus()
{
    if (m_isConnected) {
        m_statusLabel->setText(QString("状态: 已连接 - %1").arg(m_cameraManager->getCurrentIP()));
        m_statusLabel->setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; padding: 5px; }");
    } else {
        m_statusLabel->setText("状态: 未连接");
        m_statusLabel->setStyleSheet("QLabel { color: #f44336; font-weight: bold; padding: 5px; }");
    }
}

void MainWindow::updateButtonStates()
{
    m_connectButton->setEnabled(!m_isConnected);
    m_disconnectButton->setEnabled(m_isConnected);
    m_ipLineEdit->setEnabled(!m_isConnected);
}