#ifndef _HC_NET_SDK_H_
#define _HC_NET_SDK_H_

// 简化的海康威视SDK头文件模拟
// 注意：这只是为了编译通过的模拟文件
// 实际使用时需要替换为真正的海康威视SDK头文件

#ifdef _WIN32
#include <windows.h>

// 基本数据类型定义
typedef long LONG;
typedef unsigned long DWORD;
typedef unsigned char BYTE;
typedef void* LPVOID;
typedef const char* LPCSTR;

// 错误代码定义
#define NET_DVR_NOERROR                     0   // 没有错误
#define NET_DVR_PASSWORD_ERROR              1   // 用户名密码错误
#define NET_DVR_NOENOUGHPRI                 2   // 权限不够
#define NET_DVR_NOINIT                      3   // 没有初始化
#define NET_DVR_CHANNEL_ERROR               4   // 通道号错误
#define NET_DVR_OVER_MAXLINK                5   // 连接到DVR的客户端个数超过最大
#define NET_DVR_VERSIONNOMATCH              6   // 版本不匹配
#define NET_DVR_NETWORK_FAIL_CONNECT        7   // 连接服务器失败
#define NET_DVR_NETWORK_SEND_ERROR          8   // 向服务器发送失败
#define NET_DVR_NETWORK_RECV_ERROR          9   // 从服务器接收数据失败
#define NET_DVR_NETWORK_RECV_TIMEOUT        10  // 从服务器接收数据超时
#define NET_DVR_NETWORK_ERRORDATA           11  // 传送的数据有误
#define NET_DVR_ORDER_ERROR                 12  // 调用次序错误
#define NET_DVR_OPERNOPERMIT                13  // 无此权限
#define NET_DVR_COMMANDTIMEOUT              14  // DVR命令执行超时
#define NET_DVR_ERRORSERIALPORT             15  // 串口号错误
#define NET_DVR_ERRORALARMPORT              16  // 报警端口错误

// 设备信息结构体
typedef struct tagNET_DVR_DEVICEINFO_V30
{
    BYTE sSerialNumber[48];     // 序列号
    BYTE byAlarmInPortNum;      // 报警输入个数
    BYTE byAlarmOutPortNum;     // 报警输出个数
    BYTE byDiskNum;             // 硬盘个数
    BYTE byDVRType;             // 设备类型
    BYTE byChanNum;             // 模拟通道个数
    BYTE byStartChan;           // 起始通道号
    BYTE byAudioChanNum;        // 语音通道数
    BYTE byIPChanNum;           // 最大数字通道个数
    BYTE byZeroChanNum;         // 零通道编码个数
    BYTE byMainProto;           // 主码流传输协议类型
    BYTE bySubProto;            // 子码流传输协议类型
    BYTE bySupport;             // 能力
    BYTE bySupport1;            // 能力扩展
    BYTE bySupport2;            // 能力扩展
    WORD wDevType;              // 设备型号
    BYTE bySupport3;            // 能力扩展
    BYTE byMultiStreamProto;    // 是否支持多码流
    BYTE byStartDChan;          // 起始数字通道号
    BYTE byStartDTalkChan;      // 起始数字对讲通道号
    BYTE byHighDChanNum;        // 数字通道个数高位
    BYTE bySupport4;            // 能力扩展
    BYTE byLanguageType;        // 支持语种能力
    BYTE byVoiceInChanNum;      // 音频输入通道数
    BYTE byStartVoiceInChanNo;  // 音频输入起始通道号
    BYTE byRes3[2];             // 保留
    BYTE byMirrorChanNum;       // 镜像通道个数
    WORD wStartMirrorChanNo;    // 起始镜像通道号
    BYTE byRes2[2];             // 保留
} NET_DVR_DEVICEINFO_V30, *LPNET_DVR_DEVICEINFO_V30;

// 登录信息结构体
typedef struct tagNET_DVR_LOGININFO_V30
{
    char sDeviceAddress[129];   // 设备地址
    char sUserName[64];         // 用户名
    char sPassword[64];         // 密码
    WORD wPort;                 // 端口号
    BYTE bUseAsynLogin;         // 是否异步登录
    BYTE byProxyType;           // 代理类型
    BYTE byUseUTCTime;          // 是否使用UTC时间
    BYTE byLoginMode;           // 登录模式
    BYTE byHttps;               // 是否HTTPS
    LONG iProxyID;              // 代理ID
    BYTE byRes2[120];           // 保留
} NET_DVR_LOGININFO_V30, *LPNET_DVR_LOGININFO_V30;

// 预览信息结构体
typedef struct tagNET_DVR_PREVIEWINFO
{
    LONG lChannel;              // 通道号
    LONG lLinkMode;             // 连接方式
    HWND hPlayWnd;              // 播放窗口句柄
    char sMultiCastIP[16];      // 多播地址
    BYTE byProtoType;           // 协议类型
    BYTE byRes[3];              // 保留
} NET_DVR_PREVIEWINFO, *LPNET_DVR_PREVIEWINFO;

// 实时数据回调函数类型
typedef void (CALLBACK *fRealDataCallBack)(LONG lRealHandle, DWORD dwDataType, BYTE *pBuffer, DWORD dwBufSize, void *pUser);

// API函数声明
extern "C" {
    // 初始化SDK
    BOOL __stdcall NET_DVR_Init();
    
    // 清理SDK
    BOOL __stdcall NET_DVR_Cleanup();
    
    // 设置连接时间
    BOOL __stdcall NET_DVR_SetConnectTime(DWORD dwWaitTime, DWORD dwTryTimes);
    
    // 设置重连
    BOOL __stdcall NET_DVR_SetReconnect(DWORD dwInterval, BOOL bEnableRecon);
    
    // 登录设备
    LONG __stdcall NET_DVR_Login_V30(LPNET_DVR_LOGININFO_V30 pLoginInfo, LPNET_DVR_DEVICEINFO_V30 lpDeviceInfo);
    
    // 注销用户
    BOOL __stdcall NET_DVR_Logout(LONG lUserID);
    
    // 实时预览
    LONG __stdcall NET_DVR_RealPlay_V40(LONG lUserID, LPNET_DVR_PREVIEWINFO lpPreviewInfo, fRealDataCallBack fRealDataCallBack_V30, void *pUser);
    
    // 停止实时预览
    BOOL __stdcall NET_DVR_StopRealPlay(LONG lRealHandle);
    
    // 获取最后错误号
    DWORD __stdcall NET_DVR_GetLastError();
    
    // 获取错误信息
    char* __stdcall NET_DVR_GetErrorMsg(LONG *pErrorNo);
}

#endif // _WIN32

#endif // _HC_NET_SDK_H_