#include "cameramanager.h"
#include <QDebug>
#include <QApplication>
#include <QPixmap>
#include <QMutexLocker>

// Static members initialization
bool CameraManager::s_sdkInitialized = false;
int CameraManager::s_instanceCount = 0;

CameraManager::CameraManager(QObject *parent)
    : QObject(parent)
    , m_userID(-1)
    , m_realPlayHandle(-1)
    , m_playHandle(-1)
    , m_isConnected(false)
    , m_isPlaying(false)
    , m_videoLabel(nullptr)
{
    s_instanceCount++;
}

CameraManager::~CameraManager()
{
    disconnectFromCamera();
    s_instanceCount--;
    
    if (s_instanceCount == 0 && s_sdkInitialized) {
        cleanupSDK();
    }
}

bool CameraManager::initializeSDK()
{
    if (s_sdkInitialized) {
        return true;
    }
    
#ifdef _WIN32
    // Initialize Hikvision SDK
    if (!NET_DVR_Init()) {
        setError("初始化海康威视SDK失败");
        return false;
    }
    
    // Set connection timeout
    NET_DVR_SetConnectTime(2000, 1);
    NET_DVR_SetReconnect(10000, true);
    
    // Initialize PlayCtrl
    if (!PlayM4_InitDDraw()) {
        setError("初始化播放库失败");
        NET_DVR_Cleanup();
        return false;
    }
    
    s_sdkInitialized = true;
    return true;
#else
    setError("当前版本仅支持Windows平台");
    return false;
#endif
}

void CameraManager::cleanupSDK()
{
    if (!s_sdkInitialized) {
        return;
    }
    
#ifdef _WIN32
    PlayM4_ReleaseDDraw();
    NET_DVR_Cleanup();
    s_sdkInitialized = false;
#endif
}

bool CameraManager::connectToCamera(const QString &ip, const CameraCredentials &credentials)
{
    if (m_isConnected) {
        setError("摄像头已连接，请先断开当前连接");
        return false;
    }
    
    if (!s_sdkInitialized) {
        setError("SDK未初始化");
        return false;
    }
    
#ifdef _WIN32
    // Setup login parameters
    NET_DVR_LOGININFO_V30 loginInfo = {0};
    NET_DVR_DEVICEINFO_V30 deviceInfo = {0};
    
    // Convert QString to char array
    QByteArray ipBytes = ip.toLocal8Bit();
    QByteArray userBytes = credentials.username.toLocal8Bit();
    QByteArray passBytes = credentials.password.toLocal8Bit();
    
    strncpy_s(loginInfo.sDeviceAddress, ipBytes.constData(), sizeof(loginInfo.sDeviceAddress) - 1);
    strncpy_s(loginInfo.sUserName, userBytes.constData(), sizeof(loginInfo.sUserName) - 1);
    strncpy_s(loginInfo.sPassword, passBytes.constData(), sizeof(loginInfo.sPassword) - 1);
    loginInfo.wPort = 8000; // Default Hikvision port
    loginInfo.bUseAsynLogin = false;
    
    // Attempt login
    m_userID = NET_DVR_Login_V30(&loginInfo, &deviceInfo);
    if (m_userID < 0) {
        DWORD errorCode = NET_DVR_GetLastError();
        QString errorMsg = QString("登录失败，错误代码: %1").arg(errorCode);
        
        switch (errorCode) {
        case NET_DVR_PASSWORD_ERROR:
            errorMsg = "用户名或密码错误";
            break;
        case NET_DVR_NETWORK_FAIL_CONNECT:
            errorMsg = "网络连接失败，请检查IP地址";
            break;
        case NET_DVR_NETWORK_SEND_ERROR:
            errorMsg = "网络发送数据失败";
            break;
        case NET_DVR_NETWORK_RECV_ERROR:
            errorMsg = "网络接收数据失败";
            break;
        case NET_DVR_NETWORK_RECV_TIMEOUT:
            errorMsg = "网络接收超时";
            break;
        default:
            break;
        }
        
        setError(errorMsg);
        return false;
    }
    
    m_currentIP = ip;
    m_isConnected = true;
    emit connected();
    
    return true;
#else
    setError("当前版本仅支持Windows平台");
    return false;
#endif
}

void CameraManager::disconnectFromCamera()
{
    if (!m_isConnected) {
        return;
    }
    
    stopPreview();
    
#ifdef _WIN32
    if (m_userID >= 0) {
        NET_DVR_Logout(m_userID);
        m_userID = -1;
    }
#endif
    
    m_isConnected = false;
    m_currentIP.clear();
    emit disconnected();
}

void CameraManager::startPreview()
{
    if (!m_isConnected || m_isPlaying) {
        return;
    }
    
#ifdef _WIN32
    // Setup preview parameters
    NET_DVR_PREVIEWINFO previewInfo = {0};
    previewInfo.hPlayWnd = NULL; // We'll handle video data in callback
    previewInfo.lChannel = 1; // Channel 1
    previewInfo.dwStreamType = 0; // Main stream
    previewInfo.dwLinkMode = 0; // TCP
    previewInfo.bBlocked = 1; // Blocked
    previewInfo.bPassbackRecord = 0;
    previewInfo.byPreviewMode = 0;
    
    // Start real-time preview
    m_realPlayHandle = NET_DVR_RealPlay_V40(m_userID, &previewInfo, RealDataCallBack, this);
    if (m_realPlayHandle < 0) {
        DWORD errorCode = NET_DVR_GetLastError();
        setError(QString("开始预览失败，错误代码: %1").arg(errorCode));
        return;
    }
    
    // Get a free play port
    if (!PlayM4_GetPort(&m_playHandle)) {
        setError("获取播放端口失败");
        NET_DVR_StopRealPlay(m_realPlayHandle);
        m_realPlayHandle = -1;
        return;
    }
    
    // Set decode callback
    if (!PlayM4_SetDecCallBackEx(m_playHandle, DecCBFun, (DWORD)this, 0)) {
        setError("设置解码回调失败");
        PlayM4_FreePort(m_playHandle);
        NET_DVR_StopRealPlay(m_realPlayHandle);
        m_realPlayHandle = -1;
        m_playHandle = -1;
        return;
    }
    
    // Open stream
    if (!PlayM4_OpenStream(m_playHandle, NULL, 0, 1024 * 1024)) {
        setError("打开流失败");
        PlayM4_FreePort(m_playHandle);
        NET_DVR_StopRealPlay(m_realPlayHandle);
        m_realPlayHandle = -1;
        m_playHandle = -1;
        return;
    }
    
    // Start play
    if (!PlayM4_Play(m_playHandle, NULL)) {
        setError("开始播放失败");
        PlayM4_CloseStream(m_playHandle);
        PlayM4_FreePort(m_playHandle);
        NET_DVR_StopRealPlay(m_realPlayHandle);
        m_realPlayHandle = -1;
        m_playHandle = -1;
        return;
    }
    
    m_isPlaying = true;
#endif
}

void CameraManager::stopPreview()
{
    if (!m_isPlaying) {
        return;
    }
    
#ifdef _WIN32
    if (m_playHandle >= 0) {
        PlayM4_Stop(m_playHandle);
        PlayM4_CloseStream(m_playHandle);
        PlayM4_FreePort(m_playHandle);
        m_playHandle = -1;
    }
    
    if (m_realPlayHandle >= 0) {
        NET_DVR_StopRealPlay(m_realPlayHandle);
        m_realPlayHandle = -1;
    }
#endif
    
    m_isPlaying = false;
}

void CALLBACK CameraManager::RealDataCallBack(LONG lRealHandle, DWORD dwDataType, BYTE *pBuffer, DWORD dwBufSize, void *pUser)
{
    CameraManager *manager = static_cast<CameraManager*>(pUser);
    if (!manager || !pBuffer || dwBufSize == 0) {
        return;
    }
    
    manager->processVideoFrame(pBuffer, dwBufSize);
}

void CALLBACK CameraManager::DecCBFun(long nPort, char *pBuf, long nSize, FRAME_INFO *pFrameInfo, long nUser, long nReserved2)
{
    CameraManager *manager = reinterpret_cast<CameraManager*>(nUser);
    if (!manager || !pBuf || nSize == 0 || !pFrameInfo) {
        return;
    }
    
    // Only process video frames
    if (pFrameInfo->nType != T_YV12) {
        return;
    }
    
    QMutexLocker locker(&manager->m_frameMutex);
    
    if (!manager->m_videoLabel) {
        return;
    }
    
    // Convert YV12 to RGB and display
    int width = pFrameInfo->nWidth;
    int height = pFrameInfo->nHeight;
    
    if (width <= 0 || height <= 0) {
        return;
    }
    
    // Create QImage from YV12 data (simplified conversion)
    QImage image(width, height, QImage::Format_RGB888);
    
    // Simple YV12 to RGB conversion (you may want to use a more sophisticated method)
    BYTE *pY = (BYTE*)pBuf;
    BYTE *pU = pY + width * height;
    BYTE *pV = pU + width * height / 4;
    
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int Y = pY[y * width + x];
            int U = pU[(y/2) * (width/2) + (x/2)];
            int V = pV[(y/2) * (width/2) + (x/2)];
            
            // YUV to RGB conversion
            int R = Y + 1.402 * (V - 128);
            int G = Y - 0.344 * (U - 128) - 0.714 * (V - 128);
            int B = Y + 1.772 * (U - 128);
            
            R = qBound(0, R, 255);
            G = qBound(0, G, 255);
            B = qBound(0, B, 255);
            
            image.setPixel(x, y, qRgb(R, G, B));
        }
    }
    
    // Update video label in main thread
    QMetaObject::invokeMethod(manager, [manager, image]() {
        if (manager->m_videoLabel) {
            QPixmap pixmap = QPixmap::fromImage(image);
            manager->m_videoLabel->setPixmap(pixmap.scaled(manager->m_videoLabel->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation));
        }
    }, Qt::QueuedConnection);
}

void CameraManager::processVideoFrame(BYTE *pBuffer, DWORD dwBufSize)
{
#ifdef _WIN32
    if (m_playHandle >= 0) {
        if (!PlayM4_InputData(m_playHandle, pBuffer, dwBufSize)) {
            // Handle input error if needed
        }
    }
#endif
}

void CameraManager::setError(const QString &error)
{
    m_lastError = error;
    emit this->error(error);
}